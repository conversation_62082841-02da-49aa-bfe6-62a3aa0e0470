<style>
    .wishlist-card-link {
    text-decoration: none;
    color: inherit; /* optional: keeps text color from changing */
}

.wishlist-card-link:hover {
    text-decoration: none; /* ensures hover state also has no underline */
}
/* Empty Cart Message */
    .empty-cart-message {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        margin: 1rem 0;
    }

    .empty-cart-message .btn-primary {
        background-color: #355C3F;
        border-color: #355C3F;
    }

    .empty-cart-message .btn-primary:hover {
        background-color: #2a4730;
        border-color: #2a4730;
    }
/* Coupon Section Styles */
.coupon-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.coupon-input {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px;
    font-size: 14px;
}

.coupon-input:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.apply-coupon-btn {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.apply-coupon-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.apply-coupon-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.applied-coupon-display {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.coupon-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.coupon-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.available-coupons {
    max-height: 300px;
    overflow-y: auto;
}

/* Order Summary Enhancements */
.summary-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
}

.summary-card .title {
    color: #2c3e50;
    font-size: 1.25rem;
    margin-bottom: 20px;
}

.summary-card .describtion {
    color: #6c757d;
    font-weight: 500;
}

.btn-place-order {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 15px 25px;
    font-size: 16px;
    font-weight: 600;
    width: 100%;
    transition: all 0.3s ease;
}

.btn-place-order:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .coupon-section {
        padding: 10px;
    }

    .d-flex.mb-2 {
        flex-direction: column;
        gap: 10px;
    }

    .apply-coupon-btn {
        width: 100%;
    }
}
</style>

<section class="cart-tab-head my-5">
    <div class="container">

        <ul class="nav nav-tabs" id="purchase-tab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="home-tab" data-bs-toggle="tab" data-bs-target="#home-tab-pane"
                    type="button" role="tab" aria-controls="home-tab-pane" aria-selected="true">
                    <div class="d-flex align-items-center">
                        <div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                         <?= __('Cart') ?> 
                    </div>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="profile-tab"  data-bs-target="#profile-tab-pane"
                    type="button"  aria-controls="profile-tab-pane" aria-selected="false">
                    <div class="d-flex align-items-center">
                        <div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                        <?= __('Checkout') ?> 
                    </div>
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contact-tab" data-bs-target="#contact-tab-pane"
                    type="button"  aria-controls="contact-tab-pane" aria-selected="false">
                    <div class="d-flex align-items-center">
                        <div class="cart-img"><img src="../../img/ozone/cart.png" class="img-fluid" /></div>
                        <?= __('Order Complete') ?> 
                    </div>
                </button>
            </li>
        </ul>
    </div>
</section>

<section>
    <div class="container">
        <div class="tab-content" id="myTabContent">
            <!-- CART TAB -->
            <div class="tab-pane mb-5 fade show active" id="home-tab-pane" role="tabpanel" aria-labelledby="home-tab"
                tabindex="0">
                <div class="cart-section">
                    <div class="container py-5">
                        <div class="row g-4">
                            <!-- Cart Items List -->
                            <div class="col-lg-8">
                                <?php if (!empty($cartItems)): ?>
                                   <?php foreach ($cartItems as $item): ?>
                                        <div class="cart-card d-flex flex-column flex-md-row align-items-start mb-4">
                                            <!-- Product Image with link -->
                                            <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'product', $item['product_id']]) ?>" class="wishlist-card-link">
                                                <img src="<?= h($item['product_image']) ?>" alt="Product"
                                                    class="img-fluid rounded-4 mb-3 mb-md-0 me-md-4" />
                                            </a>

                                            <div class="purchase-cart">
                                                <!-- Product Name with link -->
                                                <h5 class="fw-bold">
                                                    <a href="<?= $this->Url->build(['controller' => 'Home', 'action' => 'product', $item['product_id']]) ?>" class="text-decoration-none">
                                                        <?= h($item['product_name']) ?>
                                                    </a>
                                                </h5>
                                                <p class="mb-1"><?= h($item['variant_name']) ?></p>

                                                <h4 class="text-success fw-bold mb-3">
                                                    <?= $this->Price->setPriceFormat($item['price']) ?>
                                                </h4>
                                                <h5><?= __('Total -') ?> <?= $this->Price->setPriceFormat($item['price'] * $item['quantity']) ?></h5>

                                                <div class="bg-light rounded-3 p-3 mb-3">
                                                    <p class="fw-bold mb-1"><?= __('Delivery and Returns') ?></p>
                                                    <small class="text-muted">
                                                        <?= __('Standard delivery 4–9 business days') ?><br>
                                                        <?= __('Orders are processed and delivered Monday–Friday (excluding public holidays)') ?>
                                                    </small>
                                                </div>

                                                <!-- Installation Checkbox -->
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input installation-charge-checkbox" type="checkbox" value="1"
                                                        id="installationCharge_<?= h($item['cart_item_id']) ?>"
                                                        name="installation_charge_<?= h($item['cart_item_id']) ?>"
                                                        data-cart-item-id="<?= h($item['cart_item_id']) ?>"
                                                        <?= !empty($item['installation_charge']) && $item['installation_charge'] == 1 ? 'checked' : '' ?>>
                                                    <label class="form-check-label small" for="installationCharge_<?= h($item['cart_item_id']) ?>">
                                                        <?= __('Include Installation Charge Per Unit') ?> <?= !empty($item['installation_unit_price']) ? '('.$this->Price->setPriceFormat($item['installation_unit_price']).')' : '' ?>
                                                    </label>
                                                </div>

                                                <!-- Quantity & Remove -->
                                                <div class="d-flex align-items-center cart-remove-add-qua">
                                                    <button class="btn btn-remove" type="button"
                                                        onclick="showRemoveModal('<?= $item['cart_item_id'] ?>', '<?= h($item['product_name']) ?>')"
                                                        style="margin-bottom: 12px; margin-right: 12px;">
                                                        <i class="fas fa-trash-alt"></i>
                                                    </button>

                                                    <?php $inputId = 'unitInput_' . $item['cart_item_id']; ?>
                                                    <div class="input-group increment-decrement">
                                                        <button class="btn" type="button"
                                                            onclick="updateQuantityDynamic('<?= $inputId ?>', -1)">
                                                            <img src="../../img/ozone/minus.png" class="img-fluid" />
                                                        </button>

                                                        <input type="text" id="<?= $inputId ?>" class="form-control text-center"
                                                            value="<?= $item['quantity'] ?>" readonly
                                                            data-cart-item-id="<?= $item['cart_item_id'] ?>">

                                                        <button class="btn" type="button"
                                                            onclick="updateQuantityDynamic('<?= $inputId ?>', 1)">
                                                            <img src="../../img/ozone/plus.png" class="img-fluid" />
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>

                                <?php else: ?>
                                    <p><?= __('Your cart is empty.') ?></p>
                                <?php endif; ?>
                            </div>

                            <!-- Order Summary -->
                            <div class="col-lg-4">
                                <div class="summary-card">
                                    <h5 class="fw-bold title pb-4"> <?= __('Order Summary') ?> </h5>
                                      <?php if (!empty($cartItems)): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="describtion"> <?= __('Subtotal') ?> </span>
                                        <span class="text-success">
                                            <!-- <?= h($totalPrice) ?> QAR -->
                                              <?= $this->Price->setPriceFormat($totalPrice) ?>
                                        </span>
                                    </div>

                                    <?php if (!empty($orderSummary['tax_amount']) && $orderSummary['tax_amount'] > 0): ?>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="describtion">
                                            VAT (<?= number_format($orderSummary['tax_rate'] * 100, 0) ?>%)
                                            <!-- <small class="text-muted d-block">Saudi Arabia</small> -->
                                        </span>
                                        <span class="text-success">
                                            <?= $this->Price->setPriceFormat($orderSummary['tax_amount']) ?>
                                        </span>
                                    </div>
                                    <?php endif; ?>

                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="describtion"> <?= __('Delivery Charge') ?> </span>
                                        <span class="text-success">
                                              <?= $this->Price->setPriceFormat($siteSettings->delivery_charge) ?>
                                        </span>
                                    </div>
                                    <!-- Discount Section -->
                                    <div class="d-flex justify-content-between mb-2" id="discount-row" <?= empty($appliedCoupon) ? 'style="display: none !important;"' : '' ?>>
                                        <span class="describtion"> <?= __('Discount') ?> </span>
                                        <span class="text-success" id="discount-amount">
                                            <?= !empty($appliedCoupon) ? $this->Price->setPriceFormat($appliedCoupon['discount_amount']) : $this->Price->setPriceFormat(0) ?>
                                        </span>
                                    </div>

                                    <!-- Shipping Costs Section -->
                                    <!-- <div class="d-flex justify-content-between mb-4">
                                        <span class="describtion">Shipping Costs</span>
                                        <span class="text-success" id="shipping-cost"> <?= $this->Price->setPriceFormat(0) ?></span>
                                    </div> -->
                                    <hr>
                                    <!-- Include Common Coupon Section -->
                                    <?= $this->element('coupon_section', [
                                        'orderSummary' => $orderSummary ?? [],
                                        'availableCoupons' => $availableCoupons ?? [],
                                        'appliedCoupon' => $appliedCoupon ?? null,
                                        'showAvailableCoupons' => true
                                    ]) ?>

                                    <hr>

                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" value="" id="acceptTerms">
                                        <label class="form-check-label small" for="termsCheck">
                                           <?= __('Accept') ?> <a href="/terms-condtions" target="_blank"> <?= __('Terms and Conditions') ?> </a>
                                        </label>
                                    </div>
                                    <hr>

                                    <?php
                                    // Calculate installation total in PHP
                                    $installationTotal = 0;
                                    if (!empty($cartItems)) {
                                        foreach ($cartItems as $item) {
                                            if (!empty($item['installation_charge']) && $item['installation_charge'] == 1) {
                                                $installationTotal += (float)($item['installation_unit_price'] ?? 0) * (int)($item['quantity'] ?? 1);
                                            }
                                        }
                                    }
                                    ?>
                                    
                                    <!-- Installation Charge Row -->
                                    <div class="d-flex justify-content-between mb-2" id="installation-row" <?= $installationTotal == 0 ? 'style="display:none;"' : '' ?>>
                                        <span class="describtion"> <?= __('Installation Charge') ?> </span>
                                        <span class="text-success" id="installation-amount"><?= $this->Price->setPriceFormat($installationTotal) ?></span>
                                    </div>

                                    <?php
                                        // Use the final total from order summary which includes tax
                                        
                                        $finalTotal = $orderSummary['final_total'] + $installationTotal;
                                        
                                    ?>
                                    <button class="btn btn-place-order my-2" id="place-order-btn">
                                        <?= __('Place Order') ?> | <span id="final-total"><?= $this->Price->setPriceFormat($finalTotal) ?></span>
                                    </button>
                                    <div id="codOrderMessage" class="mt-3" style="display: none;"></div>
                                     
                                     <?php else: ?>
                                       <div class="empty-cart-message text-center py-4">
                                                <p class="text-muted"><?= __('Your cart is empty') ?> </p>
                                                <a href="<?= $this->Url->build(['controller' => 'home', 'action' => 'home']) ?>" class="btn btn-primary"> <?= __('Continue Shopping') ?> </a>
                                            </div>
                                    <?php endif; ?>

                                    <p class="secure-pay-terms small mt-3"><?= __('SECURE PAYMENTS PROVIDED BY') ?>  </p>
                                    <div class="payment-icons d-flex align-items-center">
                                        <img src="../../img/ozone/mastercard-1.png" alt="Mastercard">
                                        <img src="../../img/ozone/mastercard-2.png" alt="Visa">
                                        <img src="../../img/ozone/mastercard-3.png" alt="Bitcoin">
                                        <img src="../../img/ozone/mastercard-4.png" alt="PayPal">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Other tabs (Checkout, Order Complete) remain static unless needed -->
        </div>
    </div>
</section>

<!-- Remove Item Confirmation Modal -->
<div class="modal fade" id="removeItemModal" tabindex="-1" aria-labelledby="removeItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title fw-bold" id="removeItemModalLabel"> <?= __('Remove Item') ?> </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mb-3"> <?= __('Are you sure you want to remove this item?') ?> </h6>
                <p class="text-muted mb-0" id="productNameToRemove"> <?= __('Product Name') ?> </p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal"> <?= __('Cancel') ?> </button>
                <button type="button" class="btn btn-danger px-4" id="confirmRemoveBtn"> <?= __('Remove') ?> </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('place-order-btn').addEventListener('click', function () {
        const checkbox = document.getElementById('acceptTerms');
        if (checkbox.checked) {
            window.location.href = '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'address']) ?>';
        } else {
        if (!checkbox.checked) {
            showCodOrderMessage('Please accept the terms and conditions to proceed', 'error');
            return;
        }
        }
    });
     function showCodOrderMessage(message, type) {
        const codOrderMessage = document.getElementById('codOrderMessage');
        codOrderMessage.textContent = message;
        codOrderMessage.className = `mt-3 alert ${type === 'success' ? 'alert-success' : 'alert-danger'}`;
        codOrderMessage.style.display = 'block';

        // Hide message after 5 seconds (except for success messages)
        if (type !== 'success') {
            setTimeout(() => {
                codOrderMessage.style.display = 'none';
            }, 5000);
        }
    }
</script>

<script>
// Helper function to disable/enable quantity buttons
function disableQuantityButtons(inputId, disable) {
    const input = document.getElementById(inputId);
    if (!input) return;
    const container = input.closest('.increment-decrement');
    if (!container) return;
    const buttons = container.querySelectorAll('button');

    buttons.forEach(button => {
        if (disable) {
            button.disabled = true;
            button.style.opacity = '0.6';
            button.style.cursor = 'not-allowed';
            const img = button.querySelector('img');
            if (img) {
                img.style.display = 'none';
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }
        } else {
            button.disabled = false;
            button.style.opacity = '1';
            button.style.cursor = 'pointer';

            // Restore original button content
            const spinner = button.querySelector('.fa-spinner');
            if (spinner) {
                // Restore original image based on button type
                const isMinusButton = button.onclick.toString().includes('-1');
                const imgSrc = isMinusButton ? '../../img/ozone/minus.png' : '../../img/ozone/plus.png';
                button.innerHTML = `<img src="${imgSrc}" class="img-fluid" />`;
            }
        }
    });

    // Also disable the input field
    input.disabled = disable;
    if (disable) {
        input.style.backgroundColor = '#f8f9fa';
        input.style.color = '#6c757d';
    } else {
        input.style.backgroundColor = '';
        input.style.color = '';
    }
}
    let cartItemToRemove = null;
    function showRemoveModal(cartItemId, productName) {
        cartItemToRemove = cartItemId;
        document.getElementById('productNameToRemove').textContent = productName;

    const modal = new bootstrap.Modal(document.getElementById('removeItemModal'));
    modal.show();
}
    function removeCartItem() {
        if (!cartItemToRemove) {
            console.error('No cart item ID to remove');
            return;
        }

    // Get CSRF token from multiple possible sources
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                     document.querySelector('input[name="_csrfToken"]')?.value ||
                     '<?= $this->request->getAttribute('csrfToken') ?>';

    if (!csrfToken || csrfToken === '') {
        console.error('CSRF token not found');
        showToastMessage('Error: Security token not found', 'error');
        return;
    }

        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= $this->Url->build(['controller' => 'Cart', 'action' => 'remove']) ?>/' + cartItemToRemove;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_csrfToken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);
        document.body.appendChild(form);
        form.submit();
    }

    // Event listener for the confirm remove button
    document.addEventListener('DOMContentLoaded', function () {
        const confirmRemoveBtn = document.getElementById('confirmRemoveBtn');
        if (confirmRemoveBtn) {
            confirmRemoveBtn.addEventListener('click', function () {
                removeCartItem();
            });
        }
    });
</script>

<script>
// Installation charge checkbox AJAX update
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.installation-charge-checkbox').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const cartItemId = this.getAttribute('data-cart-item-id');
            const checked = this.checked ? 1 : 0;

            // Get CSRF token (CakePHP 4+)
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                              document.querySelector('meta[name="csrfToken"]')?.getAttribute('content') ||
                              document.querySelector('input[name="_csrfToken"]')?.value ||
                              '<?= $this->request->getAttribute('csrfToken') ?>';

            fetch('<?= $this->Url->build(['controller' => 'Cart', 'action' => 'updateInstallationCharge']) ?>/' + cartItemId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': csrfToken
                },
                body: JSON.stringify({ installation_charge: checked })
            })
            .then(response => response.json())
            .then(data => {
                // Optionally show a message or update totals
                // location.reload(); // Uncomment if you want to reload the page
            })
            .catch(error => {
                alert('Failed to update installation charge.');
                // Optionally revert checkbox state
                this.checked = !checked;
            });
        });
    });
});
</script>

<script>
// Update installation charge and total dynamically
function recalculateInstallationAndTotal() {
    let installationTotal = 0;
    let totalPrice = <?= (float)str_replace(',', '', $totalPrice) ?>;
    let taxAmount = <?= (float)str_replace(',', '', $orderSummary['tax_amount']) ?>;
    let deliveryCharge = <?= (float)str_replace(',', '', $orderSummary['delivery_charge']) ?>;
    let discount = <?= !empty($appliedCoupon) ? (float)$appliedCoupon['discount_amount'] : 0 ?>;

    <?php if (!empty($cartItems)): ?>
    <?php foreach ($cartItems as $item): ?>
        (function() {
            const checkbox = document.getElementById('installationCharge_<?= h($item['cart_item_id']) ?>');
            const unitPrice = <?= (float)($item['installation_unit_price'] ?? 0) ?>;
            const qty = <?= (int)($item['quantity'] ?? 1) ?>;
            if (checkbox && checkbox.checked) {
                installationTotal += unitPrice * qty;
            }
        })();
    <?php endforeach; ?>
    <?php endif; ?>

    // Update installation charge row
    const installationRow = document.getElementById('installation-row');
    const installationAmount = document.getElementById('installation-amount');
    if (installationTotal > 0) {
        installationRow.style.display = '';
        installationAmount.textContent = formatPrice(installationTotal);
    } else {
        installationRow.style.display = 'none';
        installationAmount.textContent = formatPrice(0);
    }

    // Update final total
    let finalTotal = totalPrice - discount + installationTotal + deliveryCharge + taxAmount;
    document.getElementById('final-total').textContent = formatPrice(finalTotal);
}

// Helper to format price (simple, adjust as needed)
function formatPrice(amount) {
    //return amount.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
    return Math.round(amount).toLocaleString();
}

// Attach event listeners to installation checkboxes
document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.installation-charge-checkbox').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            recalculateInstallationAndTotal();
        });
    });
    // Initial calculation
    recalculateInstallationAndTotal();
});
</script>