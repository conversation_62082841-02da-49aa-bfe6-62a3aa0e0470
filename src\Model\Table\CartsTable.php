<?php
declare(strict_types=1);

namespace App\Model\Table;

use Cake\ORM\Query\SelectQuery;
use Cake\ORM\RulesChecker;
use Cake\ORM\Table;
use Cake\Validation\Validator;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\Controller\ComponentRegistry;
use Cake\Http\ServerRequestFactory;
use Cake\I18n\FrozenTime;
/**
 * Carts Model
 *
 * @property \App\Model\Table\CustomersTable&\Cake\ORM\Association\BelongsTo $Customers
 * @property \App\Model\Table\CartItemsTable&\Cake\ORM\Association\HasMany $CartItems
 *
 * @method \App\Model\Entity\Cart newEmptyEntity()
 * @method \App\Model\Entity\Cart newEntity(array $data, array $options = [])
 * @method array<\App\Model\Entity\Cart> newEntities(array $data, array $options = [])
 * @method \App\Model\Entity\Cart get(mixed $primaryKey, array|string $finder = 'all', \Psr\SimpleCache\CacheInterface|string|null $cache = null, \Closure|string|null $cacheKey = null, mixed ...$args)
 * @method \App\Model\Entity\Cart findOrCreate($search, ?callable $callback = null, array $options = [])
 * @method \App\Model\Entity\Cart patchEntity(\Cake\Datasource\EntityInterface $entity, array $data, array $options = [])
 * @method array<\App\Model\Entity\Cart> patchEntities(iterable $entities, array $data, array $options = [])
 * @method \App\Model\Entity\Cart|false save(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method \App\Model\Entity\Cart saveOrFail(\Cake\Datasource\EntityInterface $entity, array $options = [])
 * @method iterable<\App\Model\Entity\Cart>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Cart>|false saveMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Cart>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Cart> saveManyOrFail(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Cart>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Cart>|false deleteMany(iterable $entities, array $options = [])
 * @method iterable<\App\Model\Entity\Cart>|\Cake\Datasource\ResultSetInterface<\App\Model\Entity\Cart> deleteManyOrFail(iterable $entities, array $options = [])
 *
 * @mixin \Cake\ORM\Behavior\TimestampBehavior
 */
class CartsTable extends Table
{
    protected $language;
    protected $country;
    protected $country_id;
    public function initialize(array $config): void
    {
        parent::initialize($config);
         $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;

        $this->setTable('carts');
        $this->setDisplayField('id');
        $this->setPrimaryKey('id');

        $this->addBehavior('Timestamp');

        $this->belongsTo('Customers', [
            'foreignKey' => 'customer_id',
            'joinType' => 'INNER',
        ]);
        $this->hasMany('CartItems', [
            'foreignKey' => 'cart_id',
        ]);
         $this->belongsTo('Offers');
         $this->Products = $this->getAssociation('CartItems')->getTarget()->Products;
        
    }

    /**
     * Default validation rules.
     *
     * @param \Cake\Validation\Validator $validator Validator instance.
     * @return \Cake\Validation\Validator
     */
    public function validationDefault(Validator $validator): Validator
    {
        $validator
            ->nonNegativeInteger('customer_id')
            ->allowEmptyString('customer_id');

        return $validator;
    }

   
    public function buildRules(RulesChecker $rules): RulesChecker
    {
        $rules->add($rules->existsIn(['customer_id'], 'Customers'), ['errorField' => 'customer_id']);

        return $rules;
    }




    // new method
     public function getCartData($customerId = null, $guestToken = null)
    {
        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');
        $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];

        $cart = $this->find()
            ->where($cartConditions)
            ->contain(['CartItems' => ['Products', 'ProductVariants']])
            ->first();

        $cartItems = [];
        $cartOutItems = [];
        $totalPrice = 0;
        $totalSalePrice = 0;

        if ($cart) {
            foreach ($cart->cart_items as $item) {
                $product = $item->product;
                $variant = $item->product_variant;

                $price = $product->promotion_price ?? $this->Products->getProductPrice($item->product_id);
                $salePrice = $product->sales_price ?? null;
                $installationPrice = $product->installation_charge ?? 0;


                  $productImagesTable = TableRegistry::getTableLocator()->get('ProductImages');
                  $registry = new ComponentRegistry();
                  $mediaComponent = new \App\Controller\Component\MediaComponent($registry);
                    $image = $productImagesTable->getDefaultProductImage($item->product_id);
                    $image = $image
                        ? $mediaComponent->getCloudFrontURL($image)
                        : '/img/no-img.jpg'; // fallback image


                // $image = $this->ProductImages->getDefaultProductImage($item->product_id);
                // if ($image) {
                //     $image = $this->Media->getCloudFrontURL($image);
                // }

                $unitSalePrice = $salePrice ?? $item->product->sales_price;
                $totalRowPrice = $item->quantity * $price;
                $totalRowSalePrice = $item->quantity * $unitSalePrice;

                $totalPrice += $totalRowPrice;
                $totalSalePrice += $totalRowSalePrice;

                $itemData = [
                    'installation_unit_price' => $installationPrice ?? 0,
                    'cart_item_id' => $item->id,
                    'installation_charge' => $item->installation_charge,
                    'product_id' => $item->product_id,
                    'product_variant_id' => $item->product_variant_id,
                    'product_name' => $isArabic ? $product->name_ar : $product->name,
                    'variant_name' => $isArabic ? $variant->variant_name_ar ?? $product->name_ar : $variant->name ?? $product->name,
                    'quantity' => $item->quantity,
                    'price' => $price,
                    'sale_price' => $totalRowSalePrice,
                    'total_price' => $totalRowPrice,
                    'product_image' => $image
                ];
                $cartItems[] = $itemData;
            }
        }

        return [
            'cart_id' => $cart->id ?? 0,
            'cartItems' => $cartItems,
            'cartOutItems' => $cartOutItems,
            'totalPrice' => $totalPrice,
            'totalSalePrice' => number_format($totalSalePrice, 2),
            'total_items' => count($cartItems),
            'checkCartCount' => count($cartItems) + count($cartOutItems)
        ];
    }

     public function getOrderSummary(
        $customerId = null,
        $guestToken = null,
        $shippingCost = 0.00,
        $deliveryCharge = 0.00,
        $discountAmount = 0.00,
        $couponCode = null,
        $couponType = null
    ) {
        $cartData = $this->getCartData($customerId, $guestToken);
        $subtotal = (float)$cartData['totalPrice'];
        $totalSalePrice = (float)$cartData['totalSalePrice'];
        $totalSavings = $totalSalePrice - $subtotal;

        $installationTotal = 0.00;
        foreach ($cartData['cartItems'] as $item) {
            if (!empty($item['installation_charge']) && $item['installation_charge'] == 1) {
                $installationTotal += (float)$item['installation_unit_price'] * (int)$item['quantity'];
            }
        }

        $session = \Cake\Routing\Router::getRequest()->getSession();
        $country = $session->read('siteSettings.country') ?? 'Qatar';
        $taxRate = $country === 'Saudi Arabia' ? 0.15 : 0.00;
        $taxAmount = $subtotal * $taxRate;

        $finalShippingCost = ($couponType === 'shipping' && $couponCode) ? 0 : $shippingCost;
        $finalTotal = $subtotal - $discountAmount + $finalShippingCost + $deliveryCharge + $taxAmount + $installationTotal;

        $estimated_days = Configure::read('Settings.ESTIMATED_DATE') ?? '+3 days';
        $estimatedDeliveryDate = date('d M, Y', strtotime($estimated_days));

        return [
            'cart_id' => $cartData['cart_id'],
            'cart_items' => $cartData['cartItems'],
            'total_items' => $cartData['total_items'],
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'total_savings' => $totalSavings,
            'shipping_cost' => $finalShippingCost,
            'original_shipping_cost' => $shippingCost,
            'installation_total' => $installationTotal,
            'tax_amount' => $taxAmount,
            'tax_rate' => $taxRate,
            'country' => $country,
            'final_total' => $finalTotal,
            'estimated_delivery_date' => $estimatedDeliveryDate,
            'coupon_code' => $couponCode,
            'coupon_type' => $couponType,
            'has_items' => $cartData['total_items'] > 0,
            'shipping_discount' => $couponType === 'shipping' ? $shippingCost : 0,
            'delivery_charge' => $deliveryCharge
        ];
    }




     public function getAvailableCoupons($subtotal = 0, $countryId = null, $customerId = null, $guestToken = null)
    {
        $currentDate = FrozenTime::now();

        $query = $this->Offers->find()
            ->where([
                'status' => 'A',
                'offer_start_date <=' => $currentDate,
                'offer_end_date >=' => $currentDate
            ])
            ->contain([
                'OfferProducts' => function ($q) {
                    return $q->where(['OfferProducts.status' => 'A'])
                        ->contain(['Products']);
                }
            ]);

        if ($countryId !== null) {
            $query->where(['OR' => [
                'country_id' => $countryId,
                'country_id IS' => null
            ]]);
        } else {
            $query->where(['OR' => [
                'country_id' => 1,
                'country_id IS' => null
            ]]);
        }

        $offers = $query
            ->order(['min_cart_value' => 'ASC', 'discount' => 'DESC'])
            ->toArray();

        // Get current cart items for filtering
        $cartData = $this->getCartData($customerId, $guestToken);
        $cartItems = $cartData['cartItems'];
        $cartProductIds = array_column($cartItems, 'product_id');

        $availableCoupons = [];

        foreach ($offers as $offer) {
            if (!$this->isCouponApplicableToCart($offer, $cartProductIds)) {
                continue;
            }

            $couponType = 'fixed';
            if ($offer->free_shipping == 1) {
                $couponType = 'shipping';
            } elseif ($offer->offer_type === 'Percentage') {
                $couponType = 'percentage';
            }

            $description = $offer->offer_description;
            if (empty($description)) {
                if ($couponType === 'shipping') {
                    $description = 'Free shipping on orders above ' . number_format($offer->min_cart_value, 0);
                } elseif ($couponType === 'percentage') {
                    $description = $offer->discount . '% off on orders above ' . number_format($offer->min_cart_value, 0);
                } else {
                    $description = number_format($offer->discount, 0) . ' off on orders above ' . number_format($offer->min_cart_value, 0);
                }
            }

            $applicabilityInfo = $this->getCouponApplicabilityInfo($offer);
            if (!empty($applicabilityInfo)) {
                $description .= " " . $applicabilityInfo;
            }

            $minAmount = (float)$offer->min_cart_value;
            $isApplicable = $subtotal >= $minAmount;

            $couponData = [
                'id' => $offer->id,
                'country_id' => $offer->country_id,
                'code' => $offer->offer_code,
                'type' => $couponType,
                'value' => (float)$offer->discount,
                'min_amount' => $minAmount,
                'max_discount' => (float)$offer->max_discount,
                'description' => $description,
                'offer_name' => $offer->offer_name,
                'is_applicable' => $isApplicable,
                'amount_needed' => $isApplicable ? 0 : ($minAmount - $subtotal),
                'free_shipping' => (bool)$offer->free_shipping,
                'terms_conditions' => $offer->terms_conditions,
                'valid_until' => $offer->offer_end_date ? $offer->offer_end_date->format('d M, Y') : 'No expiry',
                'applicable_to' => $applicabilityInfo
            ];

            $availableCoupons[] = $couponData;
        }

        return $availableCoupons;
    }

    protected function isCouponApplicableToCart($offer, $cartProductIds)
    {
        if (empty($offer->offer_products)) {
            return true;
        }

        if (empty($cartProductIds)) {
            return false;
        }

        foreach ($offer->offer_products as $offerProduct) {
            if (in_array($offerProduct->product_id, $cartProductIds)) {
                return true;
            }
        }

        return false;
    }

    protected function getCouponApplicabilityInfo($offer)
    {
        if (!empty($offer->offer_products)) {
            $productNames = [];
            foreach ($offer->offer_products as $offerProduct) {
                if (!empty($offerProduct->product)) {
                    $productNames[] = $offerProduct->product->name;
                }
            }
            if (!empty($productNames)) {
                if (count($productNames) === 1) {
                    return "on " . $productNames[0];
                } elseif (count($productNames) === 2) {
                    return "on " . implode(' and ', $productNames);
                } else {
                    return "on " . implode(', ', array_slice($productNames, 0, 2)) .
                        " and " . (count($productNames) - 2) . " more products";
                }
            }
        }

        return '';
    }
    
    

    public function removeCoupon($couponCode)
    {
        return [
            'is_removed' => true,
            'message' => "Coupon '{$couponCode}' has been removed successfully",
            'coupon_code' => $couponCode
        ];
    }



    //M
    public function add_new($attributes) {

        $new = $this->newEmptyEntity();
         foreach($attributes as $key => $value) {
             $new->$key = $value;
         }

         if($this->save($new)) {
             return $new->id;
         } else {
            $errors = $new->getErrors();

            //echo "<pre>"; print_r($errors);die;
             return false;
         }
    }

    public function addToCart($productId, $customerId,$quantity = 1, $variantId = null)
    {
        if (!$customerId) {
            return $this->response->withType('application/json')->withStringBody(json_encode([
                'success' => false,
                'message' => 'Invalid Customer ID.'
            ]));
        }

        $cart = $this->find()
            ->where(['customer_id' => $customerId])
            ->first();

        if (!$cart) {
            $cart = $this->newEntity([
                'customer_id' => $customerId,
            ]);
            $this->save($cart);
        }

        // Check if the item already exists
        $query = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'product_id' => $productId
            ]);

        if ($variantId === null) {
            $query->where(['product_variant_id IS' => null]);
        } else {
            $query->where(['product_variant_id' => $variantId]);
        }

        $existingItem = $query->first();

        // Fetch promotion_price
        $Products = TableRegistry::getTableLocator()->get('Products');
        $product = $Products->find()
            ->select(['promotion_price'])
            ->where(['id' => $productId])
            ->first();

        $unitPrice = $product ? $product->promotion_price : 0;

        \Cake\Log\Log::debug('AddToCart - Product ID: ' . $productId . ', Quantity to add: ' . $quantity . ', Unit Price: ' . $unitPrice);

        if ($existingItem) {
            \Cake\Log\Log::debug('Existing item found - Current quantity: ' . $existingItem->quantity . ', Current price: ' . $existingItem->price);
            $existingItem->quantity += $quantity;
            $existingItem->price = $existingItem->quantity * $unitPrice;
            \Cake\Log\Log::debug('Updated existing item - New quantity: ' . $existingItem->quantity . ', New price: ' . $existingItem->price);
        } else {
            \Cake\Log\Log::debug('Creating new cart item');
            $existingItem = $this->CartItems->newEntity([
                'cart_id' => $cart->id,
                'customer_id'=> $customerId,
                'product_id' => $productId,
                'product_variant_id' => $variantId,
                'quantity' => $quantity,
                'price' => $quantity * $unitPrice,
            ]);
            \Cake\Log\Log::debug('New cart item - Quantity: ' . $quantity . ', Price: ' . ($quantity * $unitPrice));
        }

        $saveResult = $this->CartItems->save($existingItem);
        \Cake\Log\Log::debug('Save result: ' . ($saveResult ? 'Success' : 'Failed'));

        if ($saveResult) {
            \Cake\Log\Log::debug('Final saved item - ID: ' . $existingItem->id . ', Quantity: ' . $existingItem->quantity . ', Price: ' . $existingItem->price);
        }

        return $saveResult;
    }
}
