<?php $this->append('style'); ?>
<link rel="stylesheet" href="<?= $this->Url->webroot('css/dashboard.css') ?>" />
<link rel="stylesheet" href="<?= $this->Url->webroot('css/reports.css') ?>" />
<style>
    input,
    select,
    textarea {
        width: 300px;
        padding: 5px;
    }

</style>
<?php $this->end(); ?>

<section class="section">
    <div class="section-header d-flex justify-content-between align-items-center mb-3">
        <ul class="breadcrumb breadcrumb-style mb-0">
            <li class="breadcrumb-item">
                <h4 class="page-title m-b-0"><?= __('Dashboard12') ?></h4>
            </li>
        </ul>
    </div>

    <div class="row mt-5 pe-3 date_picker">
        <div class="col-sm-5">
        </div>
        <div class="col-sm-7 text-end">
            <div class="row align-items-center mb-2">
                <div class="col-md-6">

                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-sm-6">
                        </div>
                        <div class="col-sm-6">
                            <select id="date-period" class="form-select" onchange="handleChange(this)">
                                <option value="current_month"><?= __('Current Month') ?></option>
                                <option value="last_3_months"><?= __('Last 3 Months') ?></option>
                                <option value="last_6_months"><?= __('Last 6 Months') ?></option>
                                <option value="current_year"><?= __('Current Year') ?></option>
                                <option value="4"><?= __('Custom') ?></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row align-items-center mb-2 d-none" id="datesappear">
                <form id="dateRangeForm">
                    <div class="col-sm-3">
                        <label for="from-date" class="col-form-label fw-400"><?= __('From Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="from-date" name="from-date" />
                    </div>
                    <div class="col-sm-3">
                        <label for="to-date" class="col-form-label fw-400"><?= __('To Date') ?></label>
                    </div>
                    <div class="col-sm-3">
                        <input type="date" id="to-date" name="to-date" />
                    </div>
                    <div class="col-sm-3">
                        <button class="btn btn-primary btn-sm" type="submit"><?= __('Submit') ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="section-body mt-4">
        <div class="container-fluid">
            <div class="row ">
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style1 dashboard_box">
                        <div class="card-statistic-3 bg1">
                            <div class="card-icon card-icon-large"><i class="fa fa-award"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Number of Orders') ?></h4>
                                <span id="totalOrders"><?php echo !empty($totalOrders) ? h($totalOrders).' No.s' : '0 No.s'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="orderProgressBar" role="progressbar" data-width="<?= h($percentageOrders) ?>%"
                                        aria-valuenow="<?= h($percentageOrders) ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i>&nbsp;$&nbsp;<span id="totalSalesAmount"><?php echo !empty($totalSalesAmount) ? h(number_format($totalSalesAmount, 2)) : '0.00'; ?></span></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style2 dashboard_box">
                        <div class="card-statistic-3 bg2">
                            <div class="card-icon card-icon-large"><i class="fa fa-briefcase"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Users') ?></h4>
                                <span id="totalUsers"><?php echo !empty($totalUsers) ? h($totalUsers) : '0'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="userProgressBar" role="progressbar" data-width="<?= $newUsersPercentage ?>%"
                                        aria-valuenow="<?= $newUsersPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewUsers"><?php echo !empty($newUsers) ? h($newUsers) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="userText"><?= __('New users since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style3 dashboard_box">
                        <div class="card-statistic-3 bg3">
                            <div class="card-icon card-icon-large"><i class="fa fa-globe"></i></div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Products') ?></h4>
                                <span id="totalProducts"><?php echo !empty($totalActiveProducts) ? h($totalActiveProducts) : '0'; ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="productProgressBar" role="progressbar" data-width="<?= $newShowroomsPercentage ?>%"
                                        aria-valuenow="<?= $newShowroomsPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewProducts"><?php echo !empty($newProducts) ? h($newProducts) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="productText"><?= __('New products since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-style4 dashboard_box">
                        <div class="card-statistic-3 bg4">
                            <div class="card-icon card-icon-large"><i class="fa fa-money-bill-alt"></i>
                            </div>
                            <div class="card-content">
                                <h4 class="card-title"><?= __('Showrooms') ?></h4>
                                <span id="totalShowrooms"><?php echo !empty($totalShowrooms) ? h($totalShowrooms) : '0'; ?> <?= __('Showrooms') ?></span>
                                <div class="progress mt-1 mb-1" data-height="8">
                                    <div class="progress-bar" id="showroomProgressBar" role="progressbar" data-width="<?= $newShowroomsPercentage ?>%"
                                        aria-valuenow="<?= $newShowroomsPercentage ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <p class="mb-0 text-sm">
                                    <span class="mr-2"><i class="fa fa-arrow-up"></i> <span id="totalNewShowrooms"><?php echo !empty($newShowrooms) ? h($newShowrooms) : '0'; ?></span></span>
                                    <span class="text-nowrap" id="showroomText"><?= __('New showrooms since this month') ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Order Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="order_trends_chart"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-lg-6 col-xl-6 ">
                    <div class="card graphs">
                        <div class="card-header">
                            <h4><?= __('Revenues and Expenses Trends') ?></h4>
                        </div>
                        <div class="card-body">
                            <div id="revenue_chart"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-lg-3 col-xl-3">
                    <div class="row">
                        <div class="col-12">
                            <div class="pending_actions mb-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h4><?= __('Pending Actions') ?></h4>
                                    </div>
                                    <div class="content">
                                        <ul>
                                            <?php foreach ($pending_suppliers as $supplier): ?>
                                                <li><b><?= $supplier->contact_name ?> - </b><?= __('Approval Pending') ?></li>
                                            <?php endforeach; ?>
                                            <li><b>Stock Return - </b>Approval Pending</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="pending_actions">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Refunds Pending and Returns Pending</h4>
                                    </div>
                                    <div class="content">
                                        <ul>
                                            <li><b>Refunds Pending - </b>100</li>
                                            <li><b>Returns Pending - </b>200</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-lg-9 col-xl-9">
                    <div class="card supplier_payments">
                        <div class="card-header">
                            <h4><?= __('Supplier Payments') ?></h4>
                        </div>
                        <div class="card-body">
                            <div class="media-list position-relative">
                                <div class="table-responsive" tabindex="1" style="height: 300px;
                                overflow-y: scroll;
                                outline: none;
                                touch-action: none;">
                                    <table class="table table-hover table-xl mb-0">
                                        <thead>
                                            <tr>
                                                <th><?= __('Supplier Name') ?></th>
                                                <th><?= __('Amount') ?></th>
                                                <th><?= __('Status') ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($supplier_payment as $payment): ?>
                                            <tr>
                                                <td class="text-truncate"><?php echo !empty($payment->supplier->name) ? h($payment->supplier->name) : 'N/A'; ?></td>
                                                <td class="text-truncate"><?php echo !empty($payment->amount) ? h($payment->amount) : 'N/A'; ?></td>
                                                <td class="text-truncate">
                                                    <?php
                                                    $paymentStatusMap = [
                                                        __('Paid') => ['label' => __('Paid'), 'class' => 'col-green'],
                                                        __('Pending') => ['label' => __('Pending'), 'class' => 'col-red']
                                                    ];

                                                    $payment_status = $paymentStatusMap[$payment->purchase_order_request->payment_status] ?? ['label' => 'Unknown', 'class' => 'col-red'];
                                                    ?>
                                                    <div class="badge-outline <?= $payment_status['class'] ?>">
                                                    <?= h($payment_status['label']) ?>
                                                </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4>Top 5 Products</h4>
                        </div>
                        <div class="card-footer pt-3 d-flex justify-content-center">
                            <div class="budget-price justify-content-center">
                                <div class="budget-price-square" data-width="30"></div>
                                <div class="budget-price-label">Online</div>
                            </div>
                            <div class="budget-price justify-content-center">
                                <div class="budget-price-square product_cost" data-width="30"></div>
                                <div class="budget-price-label">Showrooms</div>
                            </div>
                        </div>
                        <div class="card-body top_products" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border"
                                style="position: relative; max-height: 320px;" [perfectScrollbar]>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-3.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="float-end">
                                            <div class="font-weight-600 text-muted text-small">112 Sales
                                            </div>
                                        </div>
                                        <div class="fw-bold font-15">Mobile</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 60%;"></div>
                                                <div class="budget-price-label">$24,897</div>
                                            </div>
                                            <div class="budget-price">
                                                <div class="budget-price-square product_cost"
                                                    style="width: 40%;"></div>
                                                <div class="budget-price-label">$18,865</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-4.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="float-end">
                                            <div class="font-weight-600 text-muted text-small">49 Sales
                                            </div>
                                        </div>
                                        <div class="fw-bold font-15">Laptop</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 78%;"></div>
                                                <div class="budget-price-label">$74,568</div>
                                            </div>
                                            <div class="budget-price">
                                                <div class="budget-price-square product_cost"
                                                    style="width: 55%;"></div>
                                                <div class="budget-price-label">$65,892</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-1.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="float-end">
                                            <div class="font-weight-600 text-muted text-small">63 Sales
                                            </div>
                                        </div>
                                        <div class="fw-bold font-15">Headphone</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 38%;"></div>
                                                <div class="budget-price-label">$2,859</div>
                                            </div>
                                            <div class="budget-price">
                                                <div class="budget-price-square product_cost"
                                                    style="width: 25%;"></div>
                                                <div class="budget-price-label">$1,872</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-2.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="float-end">
                                            <div class="font-weight-600 text-muted text-small">28 Sales
                                            </div>
                                        </div>
                                        <div class="fw-bold font-15">Tablet</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 48%;"></div>
                                                <div class="budget-price-label">$11,238</div>
                                            </div>
                                            <div class="budget-price">
                                                <div class="budget-price-square product_cost"
                                                    style="width: 33%;"></div>
                                                <div class="budget-price-label">$7,564</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-5.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="float-end">
                                            <div class="font-weight-600 text-muted text-small">19 Sales
                                            </div>
                                        </div>
                                        <div class="fw-bold font-15">Camera</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 91%;"></div>
                                                <div class="budget-price-label">$7,285</div>
                                            </div>
                                            <div class="budget-price">
                                                <div class="budget-price-square product_cost"
                                                    style="width: 74%;"></div>
                                                <div class="budget-price-label">$5,147</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-sm-6 col-lg-6">
                    <div class="card" style="background-color: #ffffff !important;">
                        <div class="card-header">
                            <h4>Top 5 Product Categories</h4>
                        </div>
                        <div class="card-body product_categories" id="top-5-scroll">
                            <ul class="list-unstyled list-unstyled-border"
                                style="position: relative; max-height: 320px;" [perfectScrollbar]>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-3.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15">Mobile</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 60%;"></div>
                                                <div class="budget-price-label">$24,897</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-4.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15">Laptop</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 78%;"></div>
                                                <div class="budget-price-label">$74,568</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-1.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15">Headphone</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 38%;"></div>
                                                <div class="budget-price-label">$2,859</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-2.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15">Tablet</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 48%;"></div>
                                                <div class="budget-price-label">$11,238</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="product-list">
                                    <img class="msr-3 rounded" width="50" src="<?= $this->Url->webroot('img/product-5.png') ?>"
                                        alt="product">
                                    <div class="set-flex">
                                        <div class="fw-bold font-15">Camera</div>
                                        <div class="mt-1">
                                            <div class="budget-price">
                                                <div class="budget-price-square" style="width: 91%;"></div>
                                                <div class="budget-price-label">$7,285</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>  
    </div>
</section>
<?php $this->append('script'); ?>
<script src="<?= $this->Url->webroot('bundles/jquery-ui/jquery-ui.min.js') ?>"></script>
<!-- <script src="< ?= $this->Url->webroot('js/page/chart-apexcharts.js') ?>"></script> -->
<script src="<?= $this->Url->webroot('bundles/sweetalert/sweetalert.min.js'); ?>"></script>
<script>
    function handleChange(answer) {
        
        if (answer.value == 4) {
            document.getElementById('datesappear').classList.remove('d-none');
        } else 
        {

            document.getElementById('datesappear').classList.add('d-none');

            $.ajax({
                url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterDashboardCard']); ?>',
                type: 'GET',
                data: {
                    dateRange: answer.value
                },
                headers: {
                    'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
                },
                success: function(response) {
                    
                    if(answer.value == '<?= __('current_month') ?>')
                    {
                        var userText = '<?= __('New users since this month') ?>';
                        var productText = '<?= __('New products since this month') ?>';
                        var showroomText = '<?= __('New showrooms since this month') ?>';
                    }
                    else if(answer.value == '<?= __('last_3_months') ?>')
                    {
                        var userText = '<?= __('Users since 3 months') ?>';
                        var productText = '<?= __('Products since 3 months') ?>';
                        var showroomText = '<?= __('Showrooms since 3 months') ?>';
                    }
                    else if(answer.value == '<?= __('last_6_months') ?>')
                    {
                        var userText = '<?= __('Users since 6 months') ?>';
                        var productText = '<?= __('Products since 6 months') ?>';
                        var showroomText = '<?= __('Showrooms since 6 months') ?>';
                    }
                    else if(answer.value == '<?= __('current_year') ?>')
                    {
                        var userText = '<?= __('Users in current year') ?>';
                        var productText = '<?= __('Products in current year') ?>';
                        var showroomText = '<?= __('Showrooms in current year') ?>';
                    }

                    $('#totalOrders').text(response.totalOrders + ' No.s');
                    $('#totalSalesAmount').text(parseFloat(response.totalSalesAmount).toFixed(2));
                    $('#orderProgressBar').css('width', response.percentageOrders + '%').attr('data-width', response.percentageOrders + '%').attr('aria-valuenow', response.percentageOrders);

                    $('#totalUsers').text(response.totalUsers);
                    $('#totalNewUsers').text(response.newUsers);
                    $('#userProgressBar').css('width', response.newUsersPercentage + '%').attr('data-width', response.newUsersPercentage + '%').attr('aria-valuenow', response.newUsersPercentage);
                    $('#userText').text(userText);

                    $('#totalProducts').text(response.totalActiveProducts);
                    $('#totalNewProducts').text(response.newProducts);
                    $('#productProgressBar').css('width', response.newProductsPercentage + '%').attr('data-width', response.newProductsPercentage + '%').attr('aria-valuenow', response.newProductsPercentage);
                    $('#productText').text(productText);

                    $('#totalShowrooms').text(response.totalShowrooms);
                    $('#totalNewShowrooms').text(response.newShowrooms);
                    $('#showroomProgressBar').css('width', response.newShowroomsPercentage + '%').attr('data-width', response.newShowroomsPercentage + '%').attr('aria-valuenow', response.newShowroomsPercentage);
                    $('#showroomText').text(showroomText);
                },
                error: function() {
                    swal('<?= __('Failed') ?>', '<?= __('Failed to fetch data. Please try again.') ?>', 'error');
                }
            });
        }
    }

    $('#dateRangeForm').on('submit', function(event) {
        event.preventDefault(); // Prevent default form submission

        // Get values from the input fields
        const fromDate = $('#from-date').val();
        const toDate = $('#to-date').val();

        // Validate date inputs
        if (!fromDate || !toDate) {
            swal('<?= __('Failed') ?>', '<?= __('Both dates are required.') ?>', 'error');
            return false;
        }

        // Check if the from date is earlier than or equal to the to date
        if (new Date(fromDate) > new Date(toDate)) {
            swal('<?= __('Failed') ?>', '<?= __('The "From Date" must be earlier than or equal to the "To Date".') ?>', 'error');
            return false;
        }

        // Send AJAX request
        $.ajax({
            url: '<?= $this->Url->build(['controller' => 'Dashboards', 'action' => 'filterDashboardCardByDate']); ?>',
            method: 'GET',
            data: { fromDate: fromDate, toDate: toDate },
            headers: {
                'X-CSRF-Token': '<?= $this->request->getAttribute('csrfToken') ?>'
            },
            success: function(response) {
                
                $('#totalOrders').text(response.totalOrders + ' No.s');
                    $('#totalSalesAmount').text(parseFloat(response.totalSalesAmount).toFixed(2));
                $('#orderProgressBar').css('width', response.percentageOrders + '%').attr('data-width', response.percentageOrders + '%').attr('aria-valuenow', response.percentageOrders);

                $('#totalUsers').text(response.totalUsers);
                $('#totalNewUsers').text(response.newUsers);
                $('#userProgressBar').css('width', response.newUsersPercentage + '%').attr('data-width', response.newUsersPercentage + '%').attr('aria-valuenow', response.newUsersPercentage);
                $('#userText').text(`Users for the period`);

                $('#totalProducts').text(response.totalActiveProducts);
                $('#totalNewProducts').text(response.newProducts);
                $('#productProgressBar').css('width', response.newProductsPercentage + '%').attr('data-width', response.newProductsPercentage + '%').attr('aria-valuenow', response.newProductsPercentage);
                $('#productText').text(`Products for the period`);

                $('#totalShowrooms').text(response.totalShowrooms);
                $('#totalNewShowrooms').text(response.newShowrooms);
                $('#showroomProgressBar').css('width', response.newShowroomsPercentage + '%').attr('data-width', response.newShowroomsPercentage + '%').attr('aria-valuenow', response.newShowroomsPercentage);
                $('#showroomText').text(`Showrooms for the period`);

            },
            error: function(xhr) {
                swal('<?= __('Failed') ?>', 'An error occurred: ' + xhr.responseText, 'error');
            }
        });
    });

$(function () {
    chart1();
    chart2();
});

const months = <?= json_encode($months); ?>;
const orderCounts = <?= json_encode($orderCounts); ?>;

function chart1() {
    var options = {
        chart: {
            height: 350,
            type: "bar",
        },
        plotOptions: {
            bar: {
                horizontal: false,
                endingShape: "rounded",
                columnWidth: "20%",
            },
        },
        dataLabels: {
            enabled: false,
        },
        stroke: {
            show: true,
            width: 2,
            colors: ["transparent"],
        },
        series: [
            {
                name: "Orders",
                type: "column",
                data: orderCounts
            },
        ],
        // colors: ["#77B6EA", "#0d839b", "#f77f00"],
        colors: ["#0d839b"],
        xaxis: {
            categories: months,
            title: {
                text: "Months",
            },
            labels: {
                style: {
                    colors: "#8e8da4",
                },
            },
        },
        yaxis: {
            title: {
                text: 'Number of Orders',
            },
            labels: {
                style: {
                    color: "#8e8da4",
                },
            },
        },

        fill: {
            opacity: 1,
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val + " Orders";
                },
            },
        },
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
        },
    };

    var chart = new ApexCharts(document.querySelector("#order_trends_chart"), options);

    chart.render();
}

function chart2() {
    var options = {
        chart: {
            height: 350,
            type: "line",
        },
        series: [
            {
                name: "Revenue",
                data: <?php echo json_encode($revenueData); ?>
            },
            {
                name: "Expenses",
                data: <?php echo json_encode($expensesData); ?>
            },
        ],
        colors: ["#0d839b", "#f77f00"],
        plotOptions: {
            bar: {
                horizontal: false,
                endingShape: "rounded",
                columnWidth: "50%",
            },
        },
        dataLabels: {
            enabled: false,
        },
        stroke: {
            show: true,
            width: 2,
            colors: ["transparent"],
        },
        title: {
            text: ".",
        },
        xaxis: {
            categories: <?php echo json_encode($monthData); ?>,
            title: {
                text: "Data Period",
            },
            labels: {
                style: {
                    colors: "#8e8da4",
                },
            },
        },
        yaxis: [{
            title: {
                text: "",
            },
            labels: {
                style: {
                    color: "#000000",
                },
            },
        }, ],
        legend: {
            position: "top",
            horizontalAlign: "right",
            floating: true,
        },
    };

    var chart = new ApexCharts(document.querySelector("#revenue_chart"), options);

    chart.render();
}

</script>
<?php $this->end(); ?>