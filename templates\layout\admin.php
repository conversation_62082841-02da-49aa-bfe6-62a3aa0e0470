<?php

/**
 * CakePHP(tm) : Rapid Development Framework (https://cakephp.org)
 * Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * @copyright     Copyright (c) Cake Software Foundation, Inc. (https://cakefoundation.org)
 * @link          https://cakephp.org CakePHP(tm) Project
 * @since         0.10.0
 * @license       https://opensource.org/licenses/mit-license.php MIT License
 * @var \App\View\AppView $this
 */

$cakeDescription = 'Ozone';
?>
<!DOCTYPE html>
<html>

<head>
    <?= $this->Html->charset() ?>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>
        <?= $cakeDescription ?>:
        <?= isset($title) ? h($title) : $this->fetch('title') ?>
    </title>
    <link rel="icon" type="image/svg+xml" href="../../img/ozone/Ozonex-svg.svg">
    <?= $this->fetch('meta') ?>
    <meta name="csrf-token" content="<?= $this->request->getAttribute('csrfToken') ?>">
    <?= $this->Html->css('app.min.css') ?>
    <style>
        /* Country Filter Dropdown Styles */
        .country-filter-item {
            transition: all 0.3s ease;
        }

        .country-filter-item:hover {
            background-color: #f8f9fa;
            transform: translateX(5px);
        }

        .country-filter-item.active {
            background-color: #007bff;
            color: white;
        }

        .country-filter-item.active:hover {
            background-color: #0056b3;
            color: white;
        }

        #countryFilterDropdown {
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
        }

        #countryFilterDropdown:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }

        .dropdown-title {
            font-weight: 600;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 5px;
            padding-bottom: 5px;
        }
    </style>

    <!-- Template CSS -->
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/style.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/components.css') ?>">
    <!-- Custom style CSS -->
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/custom.css') ?>">
    <link rel="stylesheet" href="<?= $this->Url->webroot('css/backend.css') ?>">

    <?= $this->fetch('style') ?>
    <style>
        /* Loader container */
        .ax-loader-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8); /* Semi-transparent white */
            z-index: 9999; /* Ensure it appears on top of everything */
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(5px); /* Blurred background */
            transition: opacity 0.5s ease, visibility 0.5s ease;
            opacity: 1;
            visibility: visible;
        }
        .ax-loader-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }
    </style>
</head>

<body class="dark-sidebar">
    <div class="loader"></div>
    <div id="app">
        <div class="main-wrapper main-wrapper-1">
            <?php echo $this->element('header'); ?>
            <?php echo $this->element('sidebar'); ?>
            <!-- Main Content -->
            <div class="main-content">
                <section class="section">
                    <?= $this->fetch('content') ?>
                </section>
            </div>
            <?php echo $this->element('footer'); ?>
        </div>
    </div>
    <!-- General JS Scripts -->
    <script src="<?= $this->Url->webroot('js/app.min.js') ?>"></script>
    <!-- JS Libraies -->
    <script src="<?= $this->Url->webroot('bundles/apexcharts/apexcharts.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/jqvmap/dist/jquery.vmap.min.js') ?>"></script>
    <script src="<?= $this->Url->webroot('bundles/jqvmap/dist/maps/jquery.vmap.world.js') ?>"></script>
    <!-- Page Specific JS File -->
    <script src="<?= $this->Url->webroot('js/page/index.js') ?>"></script>
    <!-- Template JS File -->
    <script src="<?= $this->Url->webroot('js/scripts.js') ?>"></script>
    <!-- Custom JS File -->
    <!-- Loader Div -->

    <?= $this->fetch('script') ?>
    <script>
        window.addEventListener("load", () => {
            const loader = document.getElementById("loader");
            if (!loader) return;
            setTimeout(() => {
                loader.classList.add("hidden");
                loader.addEventListener("transitionend", () => loader.remove());
            }, 1000);
        });

        // Country Filter Functionality
        $(document).ready(function() {

            // Fix any JSON display in country dropdown on page load
            const countryText = $('#selectedCountryText').text().trim();
            if (countryText.startsWith('{') && countryText.includes('"name"')) {
                try {
                    const countryObj = JSON.parse(countryText);
                    if (countryObj.name) {
                        $('#selectedCountryText').html('<i class="fas fa-globe me-1"></i>' + countryObj.name);
                    }
                } catch (e) {
                    // If parsing fails, reset to default
                    $('#selectedCountryText').html('<i class="fas fa-globe me-1"></i><?= __('All Countries') ?>');
                }
            }
            $('.country-filter-item').on('click', function(e) {
                e.preventDefault();

                const countryId = $(this).data('country-id');
                const countryName = $(this).text().trim();
                const csrfToken = $('meta[name="csrf-token"]').attr('content');

                // Show loading state
                $('#selectedCountryText').html('<i class="fas fa-spinner fa-spin me-1"></i>Loading...');

                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'App', 'action' => 'setCountryFilter']) ?>",
                    type: 'POST',
                    data: {
                        country_id: countryId
                    },
                    headers: {
                        'X-CSRF-Token': csrfToken
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Update the dropdown text - use clean country name from response or clicked text
                            let displayText;
                            if (countryId) {
                                // Use the country name from server response if available, otherwise use clicked text
                                displayText = response.country_name || countryName.replace(/^\s*[^\s]+\s*/, '').trim(); // Remove icon from text
                            } else {
                                displayText = '<?= __('All Countries') ?>';
                            }
                            $('#selectedCountryText').html('<i class="fas fa-globe me-1"></i>' + displayText);

                            // Update active state
                            $('.country-filter-item').removeClass('active');
                            if (countryId) {
                                $('[data-country-id="' + countryId + '"]').addClass('active');
                            } else {
                                $('[data-country-id=""]').addClass('active');
                            }

                            // Show success message
                            if (typeof swal !== 'undefined') {
                                swal('<?= __('Success') ?>', response.message, 'success');
                            }

                            // Trigger custom event for pages that need to handle country change dynamically
                            $(document).trigger('countryFilterChanged', [countryId]);

                            // Reload the page to apply filter (with delay to allow custom handlers)
                            setTimeout(function() {
                                // Check if we're on product add/edit page - don't reload for these
                                const currentPath = window.location.pathname;
                                if (currentPath.includes('/products/add') || currentPath.includes('/products/edit/')) {
                                    // Don't reload for product add/edit pages
                                    return;
                                }
                                window.location.reload();
                            }, 1000);
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        // Reset to original state on error
                        $('#selectedCountryText').html('<i class="fas fa-globe me-1"></i><?= __('All Countries') ?>');

                        if (typeof swal !== 'undefined') {
                            swal('<?= __('Error') ?>', '<?= __('Failed to update country filter. Please try again.') ?>', 'error');
                        }
                    }
                });
            });
        });

    </script>
</body>

</html>
<script>
    $(document).ready(function() {
        $("#globalSearch").on("keyup", function() {
            var query = $(this).val();
            var csrfToken = $('meta[name="csrf-token"]').attr('content');
            if (query) {
                $.ajax({
                    url: "<?= $this->Url->build(['controller' => 'App', 'action' => 'globalSearch']) ?>",
                    type: 'post',
                    data: {
                        q: query
                    },
                    headers: {
                        'X-CSRF-Token': csrfToken
                    },
                    success: function(response) {
                        if (response) {
                            displayResults(response, query);
                        } else {
                            $("#searchResults").empty();
                            $("#searchResults").hide();
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        swal('<?= __('Failed') ?>', '<?= __('Failed to search. Please try again.') ?>', 'error');
                    }
                });
            } else {
                $("#searchResults").empty();
                $("#searchResults").html('<p>No results found.</p>').show();
            }
        });
    });

    function displayResults(results, query) {
        $("#searchResults").empty();
        if (results.length > 0) {
            results.forEach(function(result) {
                $("#searchResults").append(`
                <a href="${result.link}" style="color: #0d839b; text-decoration: none;">
                    <div class="result-item">
                        ${result.value}
                         <strong style="font-size: 9px; color: #f77f00;"> - ${result.display}</strong>
                    </div>
                </a>

            `);
            });
            $("#searchResults").show();
        } else {
            $("#searchResults").html('<p>No results found.</p>').show();
        }
    }

    $(document).on("click", function(event) {
        if (!$(event.target).closest(".search-container").length) {
            $("#searchResults").hide();
        }
    });
</script>
