<?php
namespace App\Controller\Component;

use Cake\Controller\Component;
use Cake\Utility\Text;
use Cake\ORM\TableRegistry;
use Cake\Core\Configure;
use Cake\I18n\FrozenTime;
use Cake\Http\ServerRequestFactory;

class CartManagerComponent extends Component
{
    protected $controller;
    protected $Carts;
    protected $CartItems;
    protected $Users;
    protected $Offers;

    protected $language;
    protected $country;
    protected $country_id;
       public function initialize(array $config): void
    {
        parent::initialize($config);
        $this->controller = $this->_registry->getController();
        $request = ServerRequestFactory::fromGlobals();
        $this->language = $request->getSession()->read('siteSettings.language') ?? 'English';
        $this->country = $request->getSession()->read('siteSettings.country') ?? 'Qatar';
        $this->country_id = $request->getSession()->read('siteSettings.country_id') ?? 1;


        $this->Carts = TableRegistry::getTableLocator()->get('Carts');
        $this->CartItems = TableRegistry::getTableLocator()->get('CartItems');
        $this->Users = TableRegistry::getTableLocator()->get('Users');
        $this->Offers = TableRegistry::getTableLocator()->get('Offers');
         $this->ProductImages = TableRegistry::getTableLocator()->get('ProductImages');
    }

    public function updateInstallationCharge($cartItemId, $installationCharge)
    {
        $cartItem = $this->CartItems->get($cartItemId);
        if ($cartItem) {
            $cartItem->installation_charge = $installationCharge;
            if ($this->CartItems->save($cartItem)) {
                return true;
            } else {
                \Cake\Log\Log::error('Failed to update installation charge for cart item ID: ' . $cartItemId);
            }
        } else {
            \Cake\Log\Log::error('Cart item not found for ID: ' . $cartItemId);
        }
        return false;
    }
    
    public function addToCart($productId, $variantId = null, $quantity = 1, $installationCharge = 0)
    {
        $session = $this->controller->getRequest()->getSession();
        $identity = $session->read('Auth.User');
        $customerId = null;

        if ($identity) {
            $user = $this->Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        $guestToken = $session->read('GuestToken');

        if (!$customerId && !$guestToken) {
            $guestToken = Text::uuid();
            $session->write('GuestToken', $guestToken);
        }

        $cart = $this->Carts->find()
            ->where($customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken])
            ->first();

        if (!$cart) {
            $cart = $this->Carts->newEntity([
                'customer_id' => $customerId,
                'guest_token' => $guestToken,
            ]);
            $this->Carts->save($cart);
        }

        // Check if the item already exists
        $query = $this->CartItems->find()
            ->where([
                'cart_id' => $cart->id,
                'product_id' => $productId,
                'installation_charge' => $installationCharge
            ]);

        if ($variantId === null) {
            $query->where(['product_variant_id IS' => null]);
        } else {
            $query->where(['product_variant_id' => $variantId]);
        }

        $existingItem = $query->first();

        // Fetch promotion_price
        $Products = TableRegistry::getTableLocator()->get('Products');
        $product = $Products->find()
            ->select(['promotion_price'])
            ->where(['id' => $productId])
            ->first();

        $unitPrice = $product ? $product->promotion_price : 0;

        

        if ($existingItem) {
           
            $existingItem->quantity += $quantity;
            $existingItem->price = $existingItem->quantity * $unitPrice;
            $existingItem->installation_charge = $installationCharge;
            
        } else {
            \Cake\Log\Log::debug('Creating new cart item');
            $existingItem = $this->CartItems->newEntity([
                'cart_id' => $cart->id,
                'customer_id'=> $customerId,
                'product_id' => $productId,
                'product_variant_id' => $variantId,
                'quantity' => $quantity,
                'price' => $quantity * $unitPrice,
                'installation_charge' => $installationCharge
            ]);
           
        }

        $saveResult = $this->CartItems->save($existingItem);
       

        return $saveResult;
    }

    public function getCartData($customerId = null, $guestToken = null)
    {
        $isArabic = ($this->language === 'ar' || $this->language === 'Arabic' || strtolower($this->language) === 'arabic');
       
            $cartConditions = $customerId ? ['customer_id' => $customerId] : ['guest_token' => $guestToken];


            $cart = $this->Carts->find()
                ->where($cartConditions)
                ->contain(['CartItems' => ['Products', 'ProductVariants']])
                ->first();


            $cartItems = [];
            $cartOutItems = [];
            $totalPrice = 0;
            $totalSalePrice = 0;

            if ($cart) {

                foreach ($cart->cart_items as $item) {
                    $price = null;
                    $salePrice = null;

                    // if ($item->product_variant_id && $item->product_variant) {
                    //     $price = $item->product_variant->promotion_price;
                    //     $salePrice = $item->product_variant->sales_price;
                    // }

                    if (!$price) {
                        $product = $item->product;
                        $price = $product->promotion_price ?? $this->controller->Products->getProductPrice($item->product_id);
                        $salePrice = $product->sales_price ?? null;
                        $installationPrice = $product->installation_charge ?? 0;
                    }

                
                    //  $image = $this->controller->ProductImages->getDefaultProductImage($item->product_id);
                    $image = $this->ProductImages->getDefaultProductImage($item->product_id);
                
                    // $availability = $this->controller->Products->getAvailabilityStatus($item->product_id);

                    if ($image) {
                        $image = $this->controller->Media->getCloudFrontURL($image);
                    }

                    // $discount = $this->controller->Products->getDiscountProduct($item->product_id, $item->product_variant_id);
                    $unitSalePrice = $salePrice ?? $item->product->sales_price;
                    $totalRowPrice = $item->quantity * $price;
                    $totalRowSalePrice = $item->quantity * $unitSalePrice;

                    $totalPrice += $totalRowPrice;
                    $totalSalePrice += $totalRowSalePrice;

                    $itemData = [
                        'installation_unit_price' => $installationPrice ?? 0,
                        'cart_item_id' => $item->id,
                        'installation_charge' => $item->installation_charge,
                        'product_id' => $item->product_id,
                        'product_variant_id' => $item->product_variant_id,
                        'product_name' =>$isArabic ?  $item->product->name_ar : $item->product->name,
                        'variant_name' =>  $isArabic ? $item->product_variant->variant_name_ar ?? $item->product->name_ar : $item->product_variant->name ?? $item->product->name,
                        'quantity' => $item->quantity,
                        'price' => $price,
                        'sale_price' => $totalRowSalePrice,
                        'total_price' =>$totalRowPrice,
                        // 'sale_price' => number_format($totalRowSalePrice, 2),
                        // 'total_price' => number_format($totalRowPrice, 2),
                    // 'discount' => $discount,
                        'product_image' => $image,
                        // 'get_available_status' => $availability
                    ];
                    $cartItems[] = $itemData;
                    // if ($availability === "In Stock") {
                    //     $cartItems[] = $itemData;
                    // } else {
                    //     $cartOutItems[] = $itemData;
                    // }
                }
            } else {
                \Cake\Log\Log::debug('No cart found for customer ID: ' . $customerId . ', guest token: ' . $guestToken);
            }

            return [
                'cart_id' => $cart->id ?? 0,
                'cartItems' => $cartItems,
                'cartOutItems' => $cartOutItems,
                'totalPrice' => $totalPrice,
                'totalSalePrice' => number_format($totalSalePrice, 2),
                // 'totalDiscountedPrice' => number_format($totalSalePrice - $totalPrice, 2),
                'total_items' => count($cartItems),
                'checkCartCount' => count($cartItems) + count($cartOutItems)
            ];
    }


    public function getOrderSummary($customerId = null, $guestToken = null, $shippingCost = 00.00,$deliveryCharge, $discountAmount = 0.00, $couponCode = null, $couponType = null)
    {
        // Get cart data
        $cartData = $this->getCartData($customerId, $guestToken);
        $subtotal = (float) str_replace(',', '', $cartData['totalPrice']);
        $totalSalePrice = (float) str_replace(',', '', $cartData['totalSalePrice']);
        $totalSavings = $totalSalePrice - $subtotal;

        // Calculate installation charges
        $installationTotal = 0.00;
        if (!empty($cartData['cartItems'])) {
            foreach ($cartData['cartItems'] as $item) {
                if (!empty($item['installation_charge']) && $item['installation_charge'] == 1) {
                    $installationUnitPrice = (float)($item['installation_unit_price'] ?? 0);
                    $quantity = (int)($item['quantity'] ?? 1);
                    $installationTotal += $installationUnitPrice * $quantity;
                }
            }
        }

        // Calculate tax based on country
        $session = $this->getController()->getRequest()->getSession();
        $country = $session->read('siteSettings.country') ?? 'Qatar';
        $taxAmount = 0.00;
        $taxRate = 0.00;

        if ($country === 'Saudi Arabia') {
            $taxRate = 0.15; // 15% VAT for Saudi Arabia
            $taxAmount = $subtotal * $taxRate;
        }

        // Handle shipping discount for shipping coupons
        $finalShippingCost = $shippingCost;
        if ($couponType === 'shipping' && !empty($couponCode)) {
            $finalShippingCost = 0; // Free shipping
        }

        $finalTotal = $subtotal - $discountAmount + $finalShippingCost + $deliveryCharge + $taxAmount + $installationTotal;
        $estimated_days = Configure::read('Settings.ESTIMATED_DATE');
        $estimatedDeliveryDate = date('d M, Y', strtotime($estimated_days));

        return [
            'cart_id' => $cartData['cart_id'],
            'cart_items' => $cartData['cartItems'],
            'total_items' => $cartData['total_items'],
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'total_savings' => $totalSavings,
            'shipping_cost' => $finalShippingCost,
            'original_shipping_cost' => $shippingCost,
            'installation_total' => $installationTotal,
            'tax_amount' => $taxAmount,
            'tax_rate' => $taxRate,
            'country' => $country,
            'final_total' => $finalTotal,
            'estimated_delivery_date' => $estimatedDeliveryDate,
            'coupon_code' => $couponCode,
            'coupon_type' => $couponType,
            'has_items' => $cartData['total_items'] > 0,
            'shipping_discount' => $couponType === 'shipping' ? $shippingCost : 0,
            'delivery_charge'=> $deliveryCharge
        ];
    }


    public function applyCoupon($couponCode, $subtotal,$countryId=null)
    {
        $discountAmount = 0;
        $isValid = false;
        $message = 'Invalid coupon code';
        $couponType = '';
        $discountValue = 0;
        $couponDetails = null;

        $couponCode = strtoupper(trim($couponCode));
        $currentDate = date('Y-m-d H:i:s');
    
        // Fetch coupon from database with product relationships only
        $offer = $this->Offers->find()
            ->where([
                'offer_code' => $couponCode,
                'status' => 'A',
                'min_cart_value <=' => $subtotal,
                'offer_start_date <=' => $currentDate,
                'OR' => [
                    'offer_end_date IS' => null,
                    'offer_end_date >=' => $currentDate
                ]
            ])
            ->contain([
                'OfferProducts' => function ($q) {
                    return $q->where(['OfferProducts.status' => 'A'])
                        ->contain(['Products']);
                }
            ])
            ->first();

        if ($offer) {
            // Validate cart products against coupon restrictions
            $cartValidation = $this->validateCartProductsForCoupon($offer);

            if (!$cartValidation['is_valid']) {
                $message = $cartValidation['message'];
            } else {
                $isValid = true;
                $couponDetails = [
                'id' => $offer->id,
                'offer_name' => $offer->offer_name,
                'offer_code' => $offer->offer_code,
                'description' => $offer->offer_description,
                'min_amount' => (float) $offer->min_cart_value,
                'max_discount' => (float) $offer->max_discount,
                'free_shipping' => (bool) $offer->free_shipping,
                'terms_conditions' => $offer->terms_conditions
            ];

            // Determine coupon type based on offer_type and free_shipping
            if ($offer->free_shipping == 1) {
                $couponType = 'shipping';
                $discountAmount = 0; // Shipping discount handled separately
                $discountValue = 0;
                $message = "Coupon applied! Free shipping";
            } elseif ($offer->offer_type === 'Percentage') {
                $couponType = 'percentage';
                $discountValue = (float) $offer->discount;
                $discountAmount = ($subtotal * $discountValue) / 100;

                // Apply max discount limit if set
                if (!empty($offer->max_discount) && $discountAmount > (float) $offer->max_discount) {
                    $discountAmount = (float) $offer->max_discount;
                    $message = "Coupon applied! {$discountValue}% discount (Max " . number_format($offer->max_discount, 2) . ")";
                } else {
                    $message = "Coupon applied! {$discountValue}% discount";
                }
            } else {
                // Fixed amount discount
                $couponType = 'fixed';
                $discountAmount = (float) $offer->discount;
                $discountValue = $discountAmount;
                $message = "Coupon applied! " . number_format($discountAmount, 2) . " QAR discount";
                }
            }
        } else {
            // Check if coupon exists but doesn't meet criteria
            $existingOffer = $this->Offers->find()
                ->where([
                    'offer_code' => $couponCode,
                    'status' => 'A'
                ])
                ->first();
          
           
            if ($existingOffer) {
                if ($subtotal < (float) $existingOffer->min_cart_value) {
                    $message = "Minimum order amount of " . number_format($existingOffer->min_cart_value, 2) . "  required for this coupon";
                } elseif ($existingOffer->offer_start_date > $currentDate) {
                    $message = "This coupon is not yet active. Valid from " . $existingOffer->offer_start_date->format('d M, Y');
                } elseif ($existingOffer->offer_end_date && $existingOffer->offer_end_date < $currentDate) {
                    $message = "This coupon has expired on " . $existingOffer->offer_end_date->format('d M, Y');
                } else {
                    $message = "This coupon is not applicable for online orders";
                }
            } else {
                $message = "Invalid coupon code. Please check and try again.";
            }
        }

        return [
            'is_valid' => $isValid,
            'discount_amount' => round($discountAmount, 2),
            'discount_value' => $discountValue,
            'coupon_type' => $couponType,
            'message' => $message,
            'coupon_code' => $couponCode,
            'coupon_details' => $couponDetails,
            'offer_id' => $offer ? $offer->id : null
        ];
    }

   
    protected function validateCartProductsForCoupon($offer)
    {
        // If coupon has no product restrictions, it's valid for all products (universal coupon)
        if (empty($offer->offer_products)) {
            return ['is_valid' => true, 'message' => ''];
        }

        // Get current cart items
        $session = $this->getController()->getRequest()->getSession();
        $identity = $session->read('Auth.User');
        $customerId = null;

        // Get customer ID if logged in
        if ($identity && isset($identity->id) && !empty($identity->id)) {
            $Users = $this->getController()->fetchTable('Users');
            $user = $Users->find()
                ->contain(['Customers'])
                ->where(['Users.status' => 'A', 'Users.id' => $identity->id])
                ->first();

            if ($user && $user->customer) {
                $customerId = $user->customer->id;
            }
        }

        $guestToken = $session->read('GuestToken');

        $cartData = $this->getCartData($customerId, $guestToken);
        $cartItems = $cartData['cartItems'];

        if (empty($cartItems)) {
            return ['is_valid' => false, 'message' => 'Your cart is empty'];
        }

        $validProductIds = [];
        $cartProductIds = array_column($cartItems, 'product_id');

        // Check only specific products (no category checking)
        foreach ($offer->offer_products as $offerProduct) {
            if (in_array($offerProduct->product_id, $cartProductIds)) {
                $validProductIds[] = $offerProduct->product_id;
            }
        }

        // If no valid products found, coupon is not applicable
        if (empty($validProductIds)) {
            return [
                'is_valid' => false,
                'message' => 'This coupon is only applicable to specific products not in your cart.'
            ];
        }

        return ['is_valid' => true, 'message' => ''];
    }

    public function removeCoupon($couponCode)
    {
        return [
            'is_removed' => true,
            'message' => "Coupon '{$couponCode}' has been removed successfully",
            'coupon_code' => $couponCode
        ];
    }

    /**
     * Get available coupons for display from database
     * @param float $subtotal Current cart subtotal
     * @param int|null $countryId Filter coupons by country ID
     * @param int|null $customerId Customer ID for cart filtering
     * @param string|null $guestToken Guest token for cart filtering
     * @return array List of available coupons with their details
     */
    public function getAvailableCoupons($subtotal = 0, $countryId = null, $customerId = null, $guestToken = null)
    {
       // dd($countryId);
        $currentDate = FrozenTime::now();
       
       // $currentDate = date('Y-m-d H:i:s');
        $query = $this->Offers->find()
            ->where([
            'status' => 'A',
            'offer_start_date <=' => $currentDate,
            'offer_end_date >=' => $currentDate,
           ])
            ->where([
                'status' => 'A',
            ])
            ->contain([
                'OfferProducts' => function ($q) {
                    return $q->where(['OfferProducts.status' => 'A'])
                        ->contain(['Products']);
                }
            ]);

        // Add country filter if provided
        if ($countryId !== null) {
            $query->where([
                'OR' => [
                    'country_id' => $countryId,
                    'country_id IS' => null
                ]
            ]);
        }
        else{
             $query->where([
                'OR' => [
                    'country_id' => 1,
                    'country_id IS' => null
                ]
            ]);
        }

        $offers = $query->order(['min_cart_value' => 'ASC', 'discount' => 'DESC'])
            ->toArray();
           

        // Get current cart items for filtering
        $cartData = $this->getCartData($customerId, $guestToken);
        $cartItems = $cartData['cartItems'];
        $cartProductIds = array_column($cartItems, 'product_id');
        

        $availableCoupons = [];

        foreach ($offers as $offer) {
            // Check if coupon is applicable to current cart items
            if (!$this->isCouponApplicableToCart($offer, $cartProductIds)) {
                continue; // Skip this coupon if not applicable to cart
            }

            // Determine coupon type
            $couponType = 'fixed'; // default
            if ($offer->free_shipping == 1) {
                $couponType = 'shipping';
            } elseif ($offer->offer_type === 'Percentage') {
                $couponType = 'percentage';
            }

            // Format description
            $description = $offer->offer_description;
            if (empty($description)) {
                if ($couponType === 'shipping') {
                    $description = 'Free shipping on orders above ' . number_format($offer->min_cart_value, 0);
                } elseif ($couponType === 'percentage') {
                    $description = $offer->discount . '% off on orders above ' . number_format($offer->min_cart_value, 0);
                } else {
                    $description = number_format($offer->discount, 0) . ' off on orders above ' . number_format($offer->min_cart_value, 0);
                }
            }

            // Add applicable products/categories info
            $applicabilityInfo = $this->getCouponApplicabilityInfo($offer);
            if (!empty($applicabilityInfo)) {
                $description .= " " . $applicabilityInfo;
            }

            $minAmount = (float) $offer->min_cart_value;
            $isApplicable = $subtotal >= $minAmount;

            $couponData = [
                'id' => $offer->id,
                'country_id' => $offer->country_id,
                'code' => $offer->offer_code,
                'type' => $couponType,
                'value' => (float) $offer->discount,
                'min_amount' => $minAmount,
                'max_discount' => (float) $offer->max_discount,
                'description' => $description,
                'offer_name' => $offer->offer_name,
                'is_applicable' => $isApplicable,
                'amount_needed' => $isApplicable ? 0 : ($minAmount - $subtotal),
                'free_shipping' => (bool) $offer->free_shipping,
                'terms_conditions' => $offer->terms_conditions,
                'valid_until' => $offer->offer_end_date ? $offer->offer_end_date->format('d M, Y') : 'No expiry',
                'applicable_to' => $applicabilityInfo
            ];

            $availableCoupons[] = $couponData;
        }
  
        return $availableCoupons;
    }
  
    /**
     * Validate if a coupon can be applied
     * @param string $couponCode
     * @param float $subtotal
     * @return array Validation result
     */
    // public function validateCoupon($couponCode, $subtotal)
    // {
    //     $result = $this->applyCoupon($couponCode, $subtotal);
    //     return [
    //         'is_valid' => $result['is_valid'],
    //         'message' => $result['message'],
    //         'can_apply' => $result['is_valid']
    //     ];
    // }

    /**
     * Get all active coupons for admin/management purposes
     * @return array List of all active coupons
     */
    // public function getAllActiveCoupons()
    // {
    //     $currentDate = date('Y-m-d H:i:s');

    //     return $this->Offers->find()
    //         ->where([
    //             'status' => 'A',
    //             'offer_start_date <=' => $currentDate,
    //             'OR' => [
    //                 'offer_end_date IS' => null,
    //                 'offer_end_date >=' => $currentDate
    //             ]
    //         ])
    //         ->order(['created' => 'DESC'])
    //         ->toArray();
    // }

    /**
     * Get coupon statistics
     * @return array Coupon usage statistics
     */
    // public function getCouponStatistics()
    // {
    //     $currentDate = date('Y-m-d H:i:s');

    //     $totalCoupons = $this->Offers->find()->where(['status' => 'A'])->count();
    //     $activeCoupons = $this->Offers->find()
    //         ->where([
    //             'status' => 'A',
    //             'offer_start_date <=' => $currentDate,
    //             'OR' => [
    //                 'offer_end_date IS' => null,
    //                 'offer_end_date >=' => $currentDate
    //             ]
    //         ])
    //         ->count();

    //     $expiredCoupons = $this->Offers->find()
    //         ->where([
    //             'status' => 'A',
    //             'offer_end_date <' => $currentDate
    //         ])
    //         ->count();

    //     return [
    //         'total_coupons' => $totalCoupons,
    //         'active_coupons' => $activeCoupons,
    //         'expired_coupons' => $expiredCoupons,
    //         'upcoming_coupons' => $totalCoupons - $activeCoupons - $expiredCoupons
    //     ];
    // }

    /**
     * Check if a coupon is applicable to current cart items (Product-only filtering)
     * @param object $offer The offer/coupon object
     * @param array $cartProductIds Array of product IDs in cart
     * @return bool True if coupon is applicable to cart
     */
    protected function isCouponApplicableToCart($offer, $cartProductIds)
    {
        // If coupon has no product restrictions, it's applicable to all carts (universal coupon)
        if (empty($offer->offer_products)) {
            return true;
        }

        // If cart is empty, no product-specific coupons are applicable
        if (empty($cartProductIds)) {
            return false;
        }

        // Check only specific products (no category checking)
        foreach ($offer->offer_products as $offerProduct) {
            if (in_array($offerProduct->product_id, $cartProductIds)) {
                return true; // Found matching product
            }
        }

        return false; // No matching products found
    }

    /**
     * Get coupon applicability information for display (Product-only)
     * @param object $offer The offer/coupon object
     * @return string Applicability information
     */
    protected function getCouponApplicabilityInfo($offer)
    {
        // Only show specific products (no categories)
        if (!empty($offer->offer_products)) {
            $productNames = [];
            foreach ($offer->offer_products as $offerProduct) {
                if (!empty($offerProduct->product)) {
                    $productNames[] = $offerProduct->product->name;
                }
            }
            if (!empty($productNames)) {
                if (count($productNames) == 1) {
                    return "on " . $productNames[0];
                } elseif (count($productNames) == 2) {
                    return "on " . implode(' and ', $productNames);
                } else {
                    return "on " . implode(', ', array_slice($productNames, 0, 2)) .
                           " and " . (count($productNames) - 2) . " more products";
                }
            }
        }

        return ''; // Universal coupon (no specific products)
    }

    /**
     * Check and handle coupon validity when country changes
     * @param int $newCountryId The newly selected country ID
     * @return array Result of the country change validation
     */
    public function handleCountryChangeCouponValidation($newCountryId)
    {
        $session = $this->getController()->getRequest()->getSession();
        $appliedCoupon = $session->read('applied_coupon');

        // If no coupon is applied, nothing to validate
        if (empty($appliedCoupon) || empty($appliedCoupon['code'])) {
            return [
                'action' => 'none',
                'message' => 'No coupon applied'
            ];
        }

        // Get the applied coupon details from database
        $offer = $this->Offers->find()
            ->where([
                'offer_code' => $appliedCoupon['code'],
                'status' => 'A'
            ])
            ->first();

        if (!$offer) {
            // Coupon no longer exists, remove it
            $session->delete('applied_coupon');
            return [
                'action' => 'removed',
                'message' => 'Applied coupon is no longer valid and has been removed',
                'coupon_code' => $appliedCoupon['code']
            ];
        }

        // Check if coupon is country-specific
        if (!empty($offer->country_id)) {
            // Coupon is country-specific
            if ($offer->country_id != $newCountryId) {
                // Country doesn't match, remove the coupon
                $session->delete('applied_coupon');
                return [
                    'action' => 'removed',
                    'message' => "Coupon '{$appliedCoupon['code']}' is not valid for the selected country and has been removed",
                    'coupon_code' => $appliedCoupon['code']
                ];
            } else {
                // Country matches, keep the coupon
                return [
                    'action' => 'kept',
                    'message' => "Coupon '{$appliedCoupon['code']}' is still valid for the selected country",
                    'coupon_code' => $appliedCoupon['code']
                ];
            }
        } else {
            // Universal coupon (no country restriction), keep it
            return [
                'action' => 'kept',
                'message' => "Coupon '{$appliedCoupon['code']}' is valid for all countries",
                'coupon_code' => $appliedCoupon['code']
            ];
        }
    }

}